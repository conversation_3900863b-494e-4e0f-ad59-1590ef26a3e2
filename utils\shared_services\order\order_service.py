"""
订单服务 (OrderService)

提供订单相关的业务逻辑处理，包括：
- 订单创建和管理
- 订单状态跟踪和更新
- 订单查询和分页
- 订单数据验证和格式化
- 订单权限控制
"""

import uuid
import json
from datetime import datetime
from decimal import Decimal
from typing import Dict, Any, List, Optional, Tuple
from django.core.paginator import Paginator
from django.db import transaction
from django.db import models
from django.utils import timezone

from ..base.base_service import BaseService
from ..base.exceptions import (
    ValidationException,
    ResourceNotFoundException,
    PermissionDeniedException,
    BusinessLogicException
)
from .order_dto import OrderDTO
from api.models import Order, Goods, User


class OrderService(BaseService):
    """
    订单服务类
    
    提供订单相关的所有业务逻辑处理
    """
    
    def __init__(self, context: Dict[str, Any]):
        """
        初始化订单服务
        
        Args:
            context: 请求上下文，包含用户信息和权限
        """
        super().__init__(context)
        self.model = Order
    
    def get_order_list(
        self,
        filters: Optional[Dict[str, Any]] = None,
        pagination: Optional[Dict[str, Any]] = None,
        format_type: str = 'admin'
    ) -> Dict[str, Any]:
        """
        获取订单列表
        
        Args:
            filters: 过滤条件
            pagination: 分页参数
            format_type: 格式类型 ('admin', 'user', 'api')
            
        Returns:
            Dict[str, Any]: 订单列表数据
        """
        # 权限检查
        if not self.check_permission('read'):
            raise PermissionDeniedException('没有权限查看订单列表')
        
        try:
            # 构建查询
            query = self.model.objects.all()
            
            # 用户端只能查看自己的订单
            if self.request_type == 'user':
                query = query.filter(user=self.user_id)
            
            # 应用过滤条件
            if filters:
                query = self._apply_filters(query, filters)
            
            # 排序
            query = query.order_by('-created_at')
            
            # 分页处理
            page = pagination.get('page', 1) if pagination else 1
            page_size = pagination.get('page_size', 20) if pagination else 20
            
            paginator = Paginator(query, page_size)
            page_obj = paginator.get_page(page)
            
            # 格式化数据
            result = []
            for order in page_obj:
                order_dto = OrderDTO.from_model(order, include_items=True)
                
                if format_type == 'admin':
                    formatted_order = order_dto.to_admin_format()
                elif format_type == 'user':
                    formatted_order = order_dto.to_user_format()
                else:  # api
                    formatted_order = order_dto.to_api_format()
                
                result.append(formatted_order)
            
            return {
                'list': result,
                'total': paginator.count,
                'page': page,
                'page_size': page_size,
                'total_pages': paginator.num_pages
            }
            
        except Exception as e:
            self.logger.error(f'获取订单列表失败: {str(e)}')
            raise
    
    def get_order_detail(self, order_id: str, format_type: str = 'admin') -> Dict[str, Any]:
        """
        获取订单详情
        
        Args:
            order_id: 订单ID
            format_type: 格式类型 ('admin', 'user', 'api')
            
        Returns:
            Dict[str, Any]: 订单详情数据
        """
        # 参数验证
        if not order_id:
            raise ValidationException('订单ID不能为空')
        
        # 权限检查
        if not self.check_permission('read'):
            raise PermissionDeniedException('没有权限查看订单详情')
        
        try:
            # 查询订单
            query = self.model.objects
            
            # 用户端只能查看自己的订单
            if self.request_type == 'user':
                order = query.get(id=order_id, user=self.user_id)
            else:
                order = query.get(id=order_id)
            
            # 创建DTO并格式化
            order_dto = OrderDTO.from_model(order, include_items=True)
            
            if format_type == 'admin':
                return order_dto.to_admin_format()
            elif format_type == 'user':
                return order_dto.to_user_format()
            else:  # api
                return order_dto.to_api_format()
                
        except Order.DoesNotExist:
            raise ResourceNotFoundException('订单不存在')
        except Exception as e:
            self.logger.error(f'获取订单详情失败: {str(e)}')
            raise
    
    def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建订单
        
        Args:
            order_data: 订单数据
            
        Returns:
            Dict[str, Any]: 创建的订单信息
        """
        # 权限检查
        if not self.check_permission('write'):
            raise PermissionDeniedException('没有权限创建订单')
        
        # 数据验证
        self._validate_order_data(order_data)
        
        try:
            with transaction.atomic():
                # 生成订单ID和订单号
                order_id = str(uuid.uuid4())
                order_no = self._generate_order_no()
                
                # 验证商品信息并计算总金额
                items, total_amount = self._validate_and_calculate_items(order_data.get('items', []))
                
                # 检查库存
                self._check_stock_availability(items)
                
                # 创建订单数据
                order_detail_data = {
                    'items': items,
                    'total_amount': float(total_amount),
                    'status': '0',  # 待支付
                    'user_info': order_data.get('user_info', {}),
                    'shipping_info': order_data.get('shipping_info', {}),
                    'payment_info': order_data.get('payment_info', {}),
                    'created_by': self.user_id,
                    'created_at': datetime.now().isoformat()
                }
                
                # 创建订单
                order = Order(
                    id=order_id,
                    user=order_data.get('user_id', self.user_id),
                    order=order_no,
                    data=json.dumps(order_detail_data, ensure_ascii=False)
                )
                order.save()
                
                # 更新库存
                self._update_stock(items, 'decrease')
                
                # 创建DTO并返回
                order_dto = OrderDTO.from_model(order, include_items=True)
                
                self.logger.info(f'订单创建成功: {order_id}')
                return order_dto.to_admin_format()
                
        except Exception as e:
            self.logger.error(f'创建订单失败: {str(e)}')
            raise

    def update_order_status(self, order_id: str, new_status: str, notes: Optional[str] = None) -> Dict[str, Any]:
        """
        更新订单状态

        Args:
            order_id: 订单ID
            new_status: 新状态
            notes: 备注信息

        Returns:
            Dict[str, Any]: 更新后的订单信息
        """
        # 参数验证
        if not order_id:
            raise ValidationException('订单ID不能为空')

        if not new_status:
            raise ValidationException('订单状态不能为空')

        # 权限检查
        if not self.check_permission('write'):
            raise PermissionDeniedException('没有权限更新订单状态')

        # 状态验证
        valid_statuses = ['0', '1', '2', '3', '4', '5']
        if new_status not in valid_statuses:
            raise ValidationException(f'无效的订单状态: {new_status}')

        try:
            with transaction.atomic():
                # 查询订单
                order = self.model.objects.get(id=order_id)

                # 用户端只能操作自己的订单
                if self.request_type == 'user' and order.user != self.user_id:
                    raise PermissionDeniedException('没有权限操作此订单')

                # 从订单数据中获取状态
                order_status = '0'  # 默认状态
                if order.data:
                    try:
                        order_detail = json.loads(order.data)
                        order_status = order_detail.get('status', '0')
                    except json.JSONDecodeError:
                        pass

                # 状态转换验证
                if not self._is_valid_status_transition(order_status, new_status):
                    raise BusinessLogicException(f'不能从状态 {order_status} 转换到 {new_status}')

                # 更新状态到订单数据中
                if order.data:
                    try:
                        order_detail = json.loads(order.data)
                        order_detail['status'] = new_status
                        order.data = json.dumps(order_detail, ensure_ascii=False)
                    except json.JSONDecodeError:
                        order.data = json.dumps({'status': new_status}, ensure_ascii=False)
                else:
                    order.data = json.dumps({'status': new_status}, ensure_ascii=False)

                # 处理状态变更的业务逻辑
                self._handle_status_change(order, order_status, new_status, notes)

                order.save()

                # 创建DTO并返回
                order_dto = OrderDTO.from_model(order, include_items=True)

                self.logger.info(f'订单状态更新成功: {order_id} {order_status} -> {new_status}')
                return order_dto.to_admin_format()

        except Order.DoesNotExist:
            raise ResourceNotFoundException('订单不存在')
        except Exception as e:
            self.logger.error(f'更新订单状态失败: {str(e)}')
            raise

    def cancel_order(self, order_id: str, reason: Optional[str] = None) -> Dict[str, Any]:
        """
        取消订单

        Args:
            order_id: 订单ID
            reason: 取消原因

        Returns:
            Dict[str, Any]: 取消后的订单信息
        """
        # 参数验证
        if not order_id:
            raise ValidationException('订单ID不能为空')

        try:
            with transaction.atomic():
                # 查询订单
                order = self.model.objects.get(id=order_id)

                # 用户端只能取消自己的订单
                if self.request_type == 'user' and order.user != self.user_id:
                    raise PermissionDeniedException('没有权限取消此订单')

                # 获取当前订单状态
                order_status = '0'
                if order.data:
                    try:
                        order_detail = json.loads(order.data)
                        order_status = order_detail.get('status', '0')
                    except json.JSONDecodeError:
                        pass

                # 检查是否可以取消
                if order_status not in ['0', '1']:  # 只有待支付和支付中的订单可以取消
                    raise BusinessLogicException('当前订单状态不允许取消')

                # 恢复库存
                if order.data:
                    try:
                        order_detail = json.loads(order.data)
                        items = order_detail.get('items', [])
                        self._update_stock(items, 'increase')
                    except json.JSONDecodeError:
                        pass

                # 更新订单状态
                if order.data:
                    try:
                        order_detail = json.loads(order.data)
                        order_detail['status'] = '4'  # 已取消
                        order_detail['cancel_info'] = {
                            'reason': reason or '用户取消',
                            'cancelled_by': self.user_id,
                            'cancelled_at': datetime.now().isoformat()
                        }
                        order.data = json.dumps(order_detail, ensure_ascii=False)
                    except json.JSONDecodeError:
                        order.data = json.dumps({
                            'status': '4',
                            'cancel_info': {
                                'reason': reason or '用户取消',
                                'cancelled_by': self.user_id,
                                'cancelled_at': datetime.now().isoformat()
                            }
                        }, ensure_ascii=False)

                order.save()

                # 创建DTO并返回
                order_dto = OrderDTO.from_model(order, include_items=True)

                self.logger.info(f'订单取消成功: {order_id}')
                return order_dto.to_admin_format()

        except Order.DoesNotExist:
            raise ResourceNotFoundException('订单不存在')
        except Exception as e:
            self.logger.error(f'取消订单失败: {str(e)}')
            raise

    def search_orders(self, keyword: str, **kwargs) -> Dict[str, Any]:
        """
        搜索订单

        Args:
            keyword: 搜索关键词
            **kwargs: 其他搜索参数

        Returns:
            Dict[str, Any]: 搜索结果
        """
        filters = {'search': keyword}
        filters.update(kwargs)

        return self.get_order_list(
            filters=filters,
            pagination=kwargs.get('pagination'),
            format_type=kwargs.get('format_type', 'admin')
        )

    def get_order_statistics(self, date_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        获取订单统计信息

        Args:
            date_range: 日期范围 {'start': 'YYYY-MM-DD', 'end': 'YYYY-MM-DD'}

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 权限检查
        if not self.check_permission('read'):
            raise PermissionDeniedException('没有权限查看订单统计')

        try:
            query = self.model.objects.all()

            # 用户端只统计自己的订单
            if self.request_type == 'user':
                query = query.filter(user=self.user_id)

            # 日期范围过滤
            if date_range:
                if date_range.get('start'):
                    query = query.filter(created_at__gte=date_range['start'])
                if date_range.get('end'):
                    query = query.filter(created_at__lte=date_range['end'])

            # 统计各种状态的订单数量（从data字段中提取状态）
            total_orders = query.count()
            stats = {
                'total_orders': total_orders,
                'pending_payment': 0,
                'paid': 0,
                'shipped': 0,
                'completed': 0,
                'cancelled': 0,
                'refunded': 0
            }

            # 遍历订单统计状态
            total_amount = 0
            for order in query:
                if order.data:
                    try:
                        order_detail = json.loads(order.data)
                        status = order_detail.get('status', '0')

                        if status == '0':
                            stats['pending_payment'] += 1
                        elif status == '1':
                            stats['paid'] += 1
                            # 计算总金额
                            items = order_detail.get('items', [])
                            for item in items:
                                total_amount += item.get('subtotal', 0)
                        elif status == '2':
                            stats['shipped'] += 1
                            items = order_detail.get('items', [])
                            for item in items:
                                total_amount += item.get('subtotal', 0)
                        elif status == '3':
                            stats['completed'] += 1
                            items = order_detail.get('items', [])
                            for item in items:
                                total_amount += item.get('subtotal', 0)
                        elif status == '4':
                            stats['cancelled'] += 1
                        elif status == '5':
                            stats['refunded'] += 1
                    except json.JSONDecodeError:
                        stats['pending_payment'] += 1
                else:
                    stats['pending_payment'] += 1

            stats['total_amount'] = float(total_amount)

            return stats

        except Exception as e:
            self.logger.error(f'获取订单统计失败: {str(e)}')
            raise

    def _apply_filters(self, query, filters: Dict[str, Any]):
        """
        应用过滤条件

        Args:
            query: 查询对象
            filters: 过滤条件

        Returns:
            查询对象
        """
        # 用户ID过滤
        if filters.get('user_id'):
            query = query.filter(user=filters['user_id'])

        # 订单号搜索
        if filters.get('order_no'):
            query = query.filter(order__icontains=filters['order_no'])

        # 关键词搜索
        if filters.get('search'):
            keyword = filters['search']
            query = query.filter(
                models.Q(order__icontains=keyword) |
                models.Q(user__icontains=keyword)
            )

        # 日期范围过滤
        if filters.get('start_date'):
            query = query.filter(created_at__gte=filters['start_date'])

        if filters.get('end_date'):
            query = query.filter(created_at__lte=filters['end_date'])

        return query

    def _validate_order_data(self, order_data: Dict[str, Any]):
        """
        验证订单数据

        Args:
            order_data: 订单数据
        """
        # 必填字段检查
        if not order_data.get('items'):
            raise ValidationException('订单商品不能为空')

        # 商品数据验证
        items = order_data['items']
        if not isinstance(items, list) or len(items) == 0:
            raise ValidationException('订单必须包含至少一个商品')

        for i, item in enumerate(items):
            if not item.get('id'):
                raise ValidationException(f'第{i+1}个商品缺少ID')
            if not item.get('quantity') or int(item.get('quantity', 0)) <= 0:
                raise ValidationException(f'第{i+1}个商品数量无效')

    def _validate_and_calculate_items(self, items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], Decimal]:
        """
        验证商品信息并计算总金额

        Args:
            items: 商品列表

        Returns:
            Tuple[List[Dict[str, Any]], Decimal]: 验证后的商品列表和总金额
        """
        validated_items = []
        total_amount = Decimal('0')

        for item in items:
            # 查询商品信息
            try:
                goods = Goods.objects.get(id=item['id'], status='1')
            except Goods.DoesNotExist:
                raise ValidationException(f'商品不存在或已下架: {item["id"]}')

            quantity = int(item['quantity'])
            price = goods.price

            # 构建验证后的商品信息
            validated_item = {
                'id': goods.id,
                'name': goods.name,
                'price': float(price),
                'quantity': quantity,
                'subtotal': float(price * quantity),
                'image': goods.image or '',
                'category_id': goods.category.id if goods.category else ''
            }

            validated_items.append(validated_item)
            total_amount += price * quantity

        return validated_items, total_amount

    def _check_stock_availability(self, items: List[Dict[str, Any]]):
        """
        检查库存可用性

        Args:
            items: 商品列表
        """
        for item in items:
            try:
                goods = Goods.objects.get(id=item['id'])
                if goods.stock < item['quantity']:
                    raise BusinessLogicException(f'商品 {goods.name} 库存不足，当前库存: {goods.stock}')
            except Goods.DoesNotExist:
                raise ValidationException(f'商品不存在: {item["id"]}')

    def _update_stock(self, items: List[Dict[str, Any]], operation: str):
        """
        更新库存

        Args:
            items: 商品列表
            operation: 操作类型 ('increase', 'decrease')
        """
        for item in items:
            try:
                goods = Goods.objects.get(id=item['id'])
                quantity = item['quantity']

                if operation == 'decrease':
                    goods.stock -= quantity
                elif operation == 'increase':
                    goods.stock += quantity

                goods.save()
            except Goods.DoesNotExist:
                self.logger.warning(f'更新库存时商品不存在: {item["id"]}')

    def _generate_order_no(self) -> str:
        """
        生成订单号

        Returns:
            str: 订单号
        """
        now = datetime.now()
        timestamp = now.strftime('%Y%m%d%H%M%S')
        random_suffix = str(uuid.uuid4())[:8].upper()
        return f'ORD{timestamp}{random_suffix}'

    def _is_valid_status_transition(self, from_status: str, to_status: str) -> bool:
        """
        检查状态转换是否有效

        Args:
            from_status: 原状态
            to_status: 目标状态

        Returns:
            bool: 是否有效
        """
        # 定义有效的状态转换
        valid_transitions = {
            '0': ['1', '4'],  # 待支付 -> 已支付/已取消
            '1': ['2', '4', '5'],  # 已支付 -> 已发货/已取消/已退款
            '2': ['3'],  # 已发货 -> 已完成
            '3': ['5'],  # 已完成 -> 已退款
            '4': [],  # 已取消 -> 无
            '5': []   # 已退款 -> 无
        }

        return to_status in valid_transitions.get(from_status, [])

    def _handle_status_change(self, order, old_status: str, new_status: str, notes: Optional[str]):
        """
        处理状态变更的业务逻辑

        Args:
            order: 订单对象
            old_status: 原状态
            new_status: 新状态
            notes: 备注
        """
        # 记录状态变更历史
        if order.data:
            try:
                order_detail = json.loads(order.data)
                if 'status_history' not in order_detail:
                    order_detail['status_history'] = []

                order_detail['status_history'].append({
                    'from_status': old_status,
                    'to_status': new_status,
                    'changed_by': self.user_id,
                    'changed_at': datetime.now().isoformat(),
                    'notes': notes
                })

                order.data = json.dumps(order_detail, ensure_ascii=False)
            except json.JSONDecodeError:
                pass

        if new_status == '4':  # 取消订单
            pass
        elif new_status == '5':  # 退款
            if order.data:
                try:
                    order_detail = json.loads(order.data)
                    items = order_detail.get('items', [])
                    self._update_stock(items, 'increase')
                except json.JSONDecodeError:
                    pass